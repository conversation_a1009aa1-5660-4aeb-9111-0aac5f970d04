/**
 * Neon Effects CSS - 运营级霓虹灯效果样式库
 * Version: 2.0.0
 * Author: Enterprise Development Team
 * Description: 专业的霓虹灯效果样式库，包含多种动画和交互效果
 */

/* ===== 霓虹灯核心变量 ===== */
:root {
    /* 霓虹灯颜色调色板 */
    --neon-electric-blue: #00d4ff;
    --neon-hot-pink: #ff0080;
    --neon-lime-green: #00ff41;
    --neon-violet: #8a2be2;
    --neon-orange: #ff4500;
    --neon-crimson: #dc143c;
    --neon-gold: #ffd700;
    --neon-aqua: #00ffff;
    --neon-magenta: #ff00ff;
    --neon-spring-green: #00ff7f;
    
    /* 霓虹灯强度级别 */
    --neon-intensity-low: 0 0 5px;
    --neon-intensity-medium: 0 0 10px, 0 0 20px;
    --neon-intensity-high: 0 0 10px, 0 0 20px, 0 0 40px;
    --neon-intensity-extreme: 0 0 10px, 0 0 20px, 0 0 40px, 0 0 80px;
    
    /* 动画时长 */
    --neon-duration-fast: 0.5s;
    --neon-duration-normal: 1s;
    --neon-duration-slow: 2s;
    --neon-duration-very-slow: 4s;
}

/* ===== 霓虹灯文字效果 ===== */
.neon-text-base {
    font-family: 'Orbitron', 'Arial Black', sans-serif;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: default;
    user-select: none;
}

/* 霓虹灯文字尺寸变体 */
.neon-text-sm { font-size: 1rem; }
.neon-text-md { font-size: 1.5rem; }
.neon-text-lg { font-size: 2.5rem; }
.neon-text-xl { font-size: 4rem; }
.neon-text-xxl { font-size: 6rem; }

/* 霓虹灯文字颜色效果 */
.neon-electric-blue {
    color: var(--neon-electric-blue);
    text-shadow: var(--neon-intensity-medium) var(--neon-electric-blue);
}

.neon-electric-blue:hover {
    text-shadow: var(--neon-intensity-extreme) var(--neon-electric-blue);
    transform: scale(1.05);
}

.neon-hot-pink {
    color: var(--neon-hot-pink);
    text-shadow: var(--neon-intensity-medium) var(--neon-hot-pink);
}

.neon-hot-pink:hover {
    text-shadow: var(--neon-intensity-extreme) var(--neon-hot-pink);
    transform: scale(1.05);
}

.neon-lime-green {
    color: var(--neon-lime-green);
    text-shadow: var(--neon-intensity-medium) var(--neon-lime-green);
}

.neon-lime-green:hover {
    text-shadow: var(--neon-intensity-extreme) var(--neon-lime-green);
    transform: scale(1.05);
}

/* ===== 霓虹灯动画效果 ===== */

/* 闪烁效果 */
@keyframes neon-flicker-advanced {
    0%, 19%, 21%, 23%, 25%, 54%, 56%, 100% {
        opacity: 1;
        text-shadow: var(--neon-intensity-high) currentColor;
    }
    20%, 24%, 55% {
        opacity: 0.4;
        text-shadow: var(--neon-intensity-low) currentColor;
    }
}

.neon-flicker-advanced {
    animation: neon-flicker-advanced 3s infinite;
}

/* 呼吸效果 */
@keyframes neon-breathe {
    0%, 100% {
        text-shadow: var(--neon-intensity-medium) currentColor;
        transform: scale(1);
    }
    50% {
        text-shadow: var(--neon-intensity-extreme) currentColor;
        transform: scale(1.02);
    }
}

.neon-breathe {
    animation: neon-breathe 3s ease-in-out infinite;
}

/* 脉冲效果 */
@keyframes neon-pulse-strong {
    0%, 100% {
        text-shadow: var(--neon-intensity-low) currentColor;
        opacity: 0.7;
    }
    50% {
        text-shadow: var(--neon-intensity-extreme) currentColor;
        opacity: 1;
    }
}

.neon-pulse-strong {
    animation: neon-pulse-strong 1.5s ease-in-out infinite;
}

/* 彩虹循环效果 */
@keyframes neon-rainbow-cycle {
    0% { color: var(--neon-crimson); text-shadow: var(--neon-intensity-high) var(--neon-crimson); }
    12.5% { color: var(--neon-orange); text-shadow: var(--neon-intensity-high) var(--neon-orange); }
    25% { color: var(--neon-gold); text-shadow: var(--neon-intensity-high) var(--neon-gold); }
    37.5% { color: var(--neon-lime-green); text-shadow: var(--neon-intensity-high) var(--neon-lime-green); }
    50% { color: var(--neon-aqua); text-shadow: var(--neon-intensity-high) var(--neon-aqua); }
    62.5% { color: var(--neon-electric-blue); text-shadow: var(--neon-intensity-high) var(--neon-electric-blue); }
    75% { color: var(--neon-violet); text-shadow: var(--neon-intensity-high) var(--neon-violet); }
    87.5% { color: var(--neon-magenta); text-shadow: var(--neon-intensity-high) var(--neon-magenta); }
    100% { color: var(--neon-hot-pink); text-shadow: var(--neon-intensity-high) var(--neon-hot-pink); }
}

.neon-rainbow-cycle {
    animation: neon-rainbow-cycle 4s linear infinite;
}

/* 波浪效果 */
@keyframes neon-wave {
    0%, 100% {
        text-shadow: 
            var(--neon-intensity-low) currentColor,
            0 0 0 currentColor;
    }
    25% {
        text-shadow: 
            var(--neon-intensity-medium) currentColor,
            5px 0 10px currentColor;
    }
    50% {
        text-shadow: 
            var(--neon-intensity-high) currentColor,
            0 5px 15px currentColor;
    }
    75% {
        text-shadow: 
            var(--neon-intensity-medium) currentColor,
            -5px 0 10px currentColor;
    }
}

.neon-wave {
    animation: neon-wave 2s ease-in-out infinite;
}

/* ===== 霓虹灯边框效果 ===== */
.neon-border {
    border: 2px solid;
    border-radius: 10px;
    padding: 1rem;
    position: relative;
    overflow: hidden;
}

.neon-border::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg,
        transparent,
        currentColor,
        transparent,
        currentColor,
        transparent
    );
    animation: neon-border-spin 3s linear infinite;
    z-index: -1;
}

.neon-border::after {
    content: '';
    position: absolute;
    inset: 2px;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 8px;
    z-index: -1;
}

@keyframes neon-border-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== 霓虹灯背景效果 ===== */
.neon-bg-glow {
    position: relative;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 15px;
    overflow: hidden;
}

.neon-bg-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at center,
        rgba(0, 212, 255, 0.1) 0%,
        rgba(255, 0, 128, 0.1) 50%,
        rgba(0, 255, 65, 0.1) 100%
    );
    animation: neon-bg-pulse 4s ease-in-out infinite;
    z-index: -1;
}

@keyframes neon-bg-pulse {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.05); }
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .neon-text-xl { font-size: 2.5rem; }
    .neon-text-xxl { font-size: 3.5rem; }
    
    .neon-text-base {
        letter-spacing: 0.05em;
    }
}

@media (max-width: 480px) {
    .neon-text-lg { font-size: 1.8rem; }
    .neon-text-xl { font-size: 2rem; }
    .neon-text-xxl { font-size: 2.5rem; }
}

/* ===== 性能优化 ===== */
@media (prefers-reduced-motion: reduce) {
    .neon-flicker-advanced,
    .neon-breathe,
    .neon-pulse-strong,
    .neon-rainbow-cycle,
    .neon-wave,
    .neon-border::before,
    .neon-bg-glow::before {
        animation: none;
    }
}

/* ===== 可访问性增强 ===== */
.neon-text-base:focus {
    outline: 2px solid currentColor;
    outline-offset: 4px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --neon-intensity-low: 0 0 2px;
        --neon-intensity-medium: 0 0 4px;
        --neon-intensity-high: 0 0 6px;
        --neon-intensity-extreme: 0 0 8px;
    }
}
