:root {
    /* 默认主题颜色 */
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --background-color: #ffffff;
    --text-color: #212529;

    /* 霓虹灯效果颜色变量 */
    --neon-blue: #00f3ff;
    --neon-pink: #ff006e;
    --neon-green: #39ff14;
    --neon-purple: #bf00ff;
    --neon-orange: #ff8c00;
    --neon-red: #ff073a;
    --neon-yellow: #ffff00;
    --neon-cyan: #00ffff;

    /* 霓虹灯阴影强度 */
    --neon-glow-small: 0 0 5px;
    --neon-glow-medium: 0 0 10px, 0 0 20px;
    --neon-glow-large: 0 0 10px, 0 0 20px, 0 0 40px;
    --neon-glow-extra: 0 0 10px, 0 0 20px, 0 0 40px, 0 0 80px;
}

/* 霓虹灯基础样式 */
.neon-text {
    font-family: 'Arial', sans-serif;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: var(--neon-glow-medium) currentColor;
    transition: all 0.3s ease-in-out;
    cursor: pointer;
}

/* 霓虹灯悬停效果 */
.neon-text:hover {
    text-shadow: var(--neon-glow-large) currentColor;
    transform: scale(1.05);
}

/* 霓虹灯颜色变体 */
.neon-blue {
    color: var(--neon-blue);
    text-shadow: var(--neon-glow-medium) var(--neon-blue);
}

.neon-blue:hover {
    text-shadow: var(--neon-glow-extra) var(--neon-blue);
}

.neon-pink {
    color: var(--neon-pink);
    text-shadow: var(--neon-glow-medium) var(--neon-pink);
}

.neon-pink:hover {
    text-shadow: var(--neon-glow-extra) var(--neon-pink);
}

.neon-green {
    color: var(--neon-green);
    text-shadow: var(--neon-glow-medium) var(--neon-green);
}

.neon-green:hover {
    text-shadow: var(--neon-glow-extra) var(--neon-green);
}

.neon-purple {
    color: var(--neon-purple);
    text-shadow: var(--neon-glow-medium) var(--neon-purple);
}

.neon-purple:hover {
    text-shadow: var(--neon-glow-extra) var(--neon-purple);
}

.neon-orange {
    color: var(--neon-orange);
    text-shadow: var(--neon-glow-medium) var(--neon-orange);
}

.neon-orange:hover {
    text-shadow: var(--neon-glow-extra) var(--neon-orange);
}

.neon-red {
    color: var(--neon-red);
    text-shadow: var(--neon-glow-medium) var(--neon-red);
}

.neon-red:hover {
    text-shadow: var(--neon-glow-extra) var(--neon-red);
}

.neon-yellow {
    color: var(--neon-yellow);
    text-shadow: var(--neon-glow-medium) var(--neon-yellow);
}

.neon-yellow:hover {
    text-shadow: var(--neon-glow-extra) var(--neon-yellow);
}

.neon-cyan {
    color: var(--neon-cyan);
    text-shadow: var(--neon-glow-medium) var(--neon-cyan);
}

.neon-cyan:hover {
    text-shadow: var(--neon-glow-extra) var(--neon-cyan);
}

/* 霓虹灯动画效果 */
@keyframes neon-flicker {
    0%, 100% {
        opacity: 1;
        text-shadow: var(--neon-glow-large) currentColor;
    }
    50% {
        opacity: 0.8;
        text-shadow: var(--neon-glow-medium) currentColor;
    }
}

@keyframes neon-pulse {
    0%, 100% {
        text-shadow: var(--neon-glow-medium) currentColor;
    }
    50% {
        text-shadow: var(--neon-glow-extra) currentColor;
    }
}

@keyframes neon-rainbow {
    0% { color: var(--neon-red); text-shadow: var(--neon-glow-medium) var(--neon-red); }
    14% { color: var(--neon-orange); text-shadow: var(--neon-glow-medium) var(--neon-orange); }
    28% { color: var(--neon-yellow); text-shadow: var(--neon-glow-medium) var(--neon-yellow); }
    42% { color: var(--neon-green); text-shadow: var(--neon-glow-medium) var(--neon-green); }
    57% { color: var(--neon-cyan); text-shadow: var(--neon-glow-medium) var(--neon-cyan); }
    71% { color: var(--neon-blue); text-shadow: var(--neon-glow-medium) var(--neon-blue); }
    85% { color: var(--neon-purple); text-shadow: var(--neon-glow-medium) var(--neon-purple); }
    100% { color: var(--neon-pink); text-shadow: var(--neon-glow-medium) var(--neon-pink); }
}

/* 霓虹灯动画类 */
.neon-flicker {
    animation: neon-flicker 2s infinite;
}

.neon-pulse {
    animation: neon-pulse 1.5s ease-in-out infinite;
}

.neon-rainbow {
    animation: neon-rainbow 3s linear infinite;
}

/* 定义浅蓝主题 */
[data-bs-theme="light-blue"] {
    --primary-color: #a8e6cf;
    --secondary-color: #e3f2fd;
    --background-color: #f8f9fa;
    --text-color: #212529;
}

/* 定义浅灰主题 */
[data-bs-theme="light-gray"] {
    --primary-color: #f4f4f4;
    --secondary-color: #e0e0e0;
    --background-color: #ffffff;
    --text-color: #333333;
}

/* 定义浅木色主题 */
[data-bs-theme="light-wood"] {
    --primary-color: #e6e6c1;
    --secondary-color: #f5f5e0;
    --background-color: #ffffff;
    --text-color: #212529;
}

/* 定义浅棕色主题 */
[data-bs-theme="light-brown"] {
    --primary-color: #d3c1b1;
    --secondary-color: #e6d5c6;
    --background-color: #ffffff;
    --text-color: #212529;
}

/* 定义浅藏色主题 */
[data-bs-theme="light-cyan"] {
    --primary-color: #cfd8dc;
    --secondary-color: #e8f5f5;
    --background-color: #ffffff;
    --text-color: #212529;
}

/* 定义雾霾色主题 */
[data-bs-theme="haze"] {
    --primary-color: #e8e8e8;
    --secondary-color: #f0f0f0;
    --background-color: #ffffff;
    --text-color: #444444;
}

/* 应用主题颜色 */
body {
    background-color: var(--background-color);
    color: var(--text-color);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.card {
    background-color: var(--background-color);
    border-color: var(--secondary-color);
}
