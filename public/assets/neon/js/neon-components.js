/**
 * Neon Components - 运营级霓虹灯组件库
 * Version: 2.0.0
 * Author: Enterprise Development Team
 * Description: 可复用的霓虹灯UI组件
 */

(function() {
    'use strict';

    // ===== 霓虹灯通知组件 =====
    class NeonNotification {
        constructor(options = {}) {
            this.options = {
                type: 'info', // success, warning, error, info
                title: '',
                message: '',
                duration: 5000,
                position: 'top-right',
                showProgress: true,
                ...options
            };
            this.element = null;
            this.progressBar = null;
            this.timer = null;
        }

        show() {
            this.create();
            this.animate();
            if (this.options.duration > 0) {
                this.startTimer();
            }
            return this;
        }

        create() {
            const colors = {
                success: 'var(--neon-lime-green)',
                warning: 'var(--neon-gold)',
                error: 'var(--neon-crimson)',
                info: 'var(--neon-electric-blue)'
            };

            this.element = document.createElement('div');
            this.element.className = `neon-notification neon-notification-${this.options.type}`;
            this.element.style.cssText = `
                position: fixed;
                z-index: 10000;
                min-width: 300px;
                max-width: 400px;
                background: rgba(0, 0, 0, 0.9);
                border: 2px solid ${colors[this.options.type]};
                border-radius: 10px;
                padding: 1rem;
                color: white;
                box-shadow: 0 0 20px ${colors[this.options.type]}40;
                backdrop-filter: blur(10px);
                transform: translateX(100%);
                transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            `;

            // 设置位置
            this.setPosition();

            // 创建内容
            const content = document.createElement('div');
            content.innerHTML = `
                ${this.options.title ? `<div class="neon-notification-title" style="font-weight: bold; margin-bottom: 0.5rem; color: ${colors[this.options.type]};">${this.options.title}</div>` : ''}
                <div class="neon-notification-message">${this.options.message}</div>
                ${this.options.showProgress ? '<div class="neon-notification-progress" style="height: 3px; background: rgba(255,255,255,0.2); margin-top: 0.5rem; border-radius: 2px; overflow: hidden;"><div class="neon-notification-progress-bar" style="height: 100%; background: ' + colors[this.options.type] + '; width: 100%; transition: width linear;"></div></div>' : ''}
            `;

            // 关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
                position: absolute;
                top: 0.5rem;
                right: 0.5rem;
                background: none;
                border: none;
                color: white;
                font-size: 1.5rem;
                cursor: pointer;
                opacity: 0.7;
                transition: opacity 0.2s;
            `;
            closeBtn.addEventListener('click', () => this.hide());
            closeBtn.addEventListener('mouseenter', () => closeBtn.style.opacity = '1');
            closeBtn.addEventListener('mouseleave', () => closeBtn.style.opacity = '0.7');

            this.element.appendChild(content);
            this.element.appendChild(closeBtn);

            if (this.options.showProgress) {
                this.progressBar = this.element.querySelector('.neon-notification-progress-bar');
            }

            document.body.appendChild(this.element);
        }

        setPosition() {
            const positions = {
                'top-right': { top: '1rem', right: '1rem' },
                'top-left': { top: '1rem', left: '1rem' },
                'bottom-right': { bottom: '1rem', right: '1rem' },
                'bottom-left': { bottom: '1rem', left: '1rem' },
                'top-center': { top: '1rem', left: '50%', transform: 'translateX(-50%)' },
                'bottom-center': { bottom: '1rem', left: '50%', transform: 'translateX(-50%)' }
            };

            const pos = positions[this.options.position] || positions['top-right'];
            Object.assign(this.element.style, pos);
        }

        animate() {
            requestAnimationFrame(() => {
                this.element.style.transform = this.options.position.includes('center') ? 'translateX(-50%)' : 'translateX(0)';
            });
        }

        startTimer() {
            if (this.progressBar) {
                this.progressBar.style.transitionDuration = this.options.duration + 'ms';
                requestAnimationFrame(() => {
                    this.progressBar.style.width = '0%';
                });
            }

            this.timer = setTimeout(() => {
                this.hide();
            }, this.options.duration);
        }

        hide() {
            if (this.timer) {
                clearTimeout(this.timer);
            }

            this.element.style.transform = this.options.position.includes('left') ? 'translateX(-100%)' : 'translateX(100%)';
            this.element.style.opacity = '0';

            setTimeout(() => {
                if (this.element && this.element.parentNode) {
                    this.element.parentNode.removeChild(this.element);
                }
            }, 300);
        }
    }

    // ===== 霓虹灯模态框组件 =====
    class NeonModal {
        constructor(options = {}) {
            this.options = {
                title: '',
                content: '',
                size: 'medium', // small, medium, large
                showCloseButton: true,
                backdrop: true,
                keyboard: true,
                ...options
            };
            this.element = null;
            this.backdrop = null;
            this.isOpen = false;
        }

        show() {
            if (this.isOpen) return;
            
            this.create();
            this.animate();
            this.bindEvents();
            this.isOpen = true;
            
            document.body.style.overflow = 'hidden';
            return this;
        }

        create() {
            // 创建背景
            this.backdrop = document.createElement('div');
            this.backdrop.className = 'neon-modal-backdrop';
            this.backdrop.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                backdrop-filter: blur(5px);
                z-index: 10000;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            // 创建模态框
            const sizes = {
                small: '400px',
                medium: '600px',
                large: '800px'
            };

            this.element = document.createElement('div');
            this.element.className = 'neon-modal';
            this.element.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) scale(0.7);
                width: 90%;
                max-width: ${sizes[this.options.size]};
                max-height: 90vh;
                background: rgba(0, 0, 0, 0.9);
                border: 2px solid var(--neon-electric-blue);
                border-radius: 15px;
                box-shadow: 0 0 30px var(--neon-electric-blue)40;
                z-index: 10001;
                opacity: 0;
                transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                overflow: hidden;
            `;

            // 创建内容
            const content = document.createElement('div');
            content.className = 'neon-modal-content';
            content.style.cssText = `
                padding: 2rem;
                color: white;
                max-height: calc(90vh - 4rem);
                overflow-y: auto;
            `;

            if (this.options.title) {
                const title = document.createElement('h3');
                title.className = 'neon-modal-title';
                title.textContent = this.options.title;
                title.style.cssText = `
                    margin: 0 0 1rem 0;
                    color: var(--neon-electric-blue);
                    text-shadow: 0 0 10px var(--neon-electric-blue);
                `;
                content.appendChild(title);
            }

            const body = document.createElement('div');
            body.className = 'neon-modal-body';
            body.innerHTML = this.options.content;
            content.appendChild(body);

            if (this.options.showCloseButton) {
                const closeBtn = document.createElement('button');
                closeBtn.innerHTML = '×';
                closeBtn.className = 'neon-modal-close';
                closeBtn.style.cssText = `
                    position: absolute;
                    top: 1rem;
                    right: 1rem;
                    background: none;
                    border: none;
                    color: white;
                    font-size: 2rem;
                    cursor: pointer;
                    opacity: 0.7;
                    transition: all 0.2s;
                    width: 40px;
                    height: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                `;
                closeBtn.addEventListener('click', () => this.hide());
                closeBtn.addEventListener('mouseenter', () => {
                    closeBtn.style.opacity = '1';
                    closeBtn.style.background = 'rgba(255, 255, 255, 0.1)';
                });
                closeBtn.addEventListener('mouseleave', () => {
                    closeBtn.style.opacity = '0.7';
                    closeBtn.style.background = 'none';
                });
                this.element.appendChild(closeBtn);
            }

            this.element.appendChild(content);
            document.body.appendChild(this.backdrop);
            document.body.appendChild(this.element);
        }

        animate() {
            requestAnimationFrame(() => {
                this.backdrop.style.opacity = '1';
                this.element.style.opacity = '1';
                this.element.style.transform = 'translate(-50%, -50%) scale(1)';
            });
        }

        bindEvents() {
            if (this.options.backdrop) {
                this.backdrop.addEventListener('click', () => this.hide());
            }

            if (this.options.keyboard) {
                this.keydownHandler = (e) => {
                    if (e.key === 'Escape') {
                        this.hide();
                    }
                };
                document.addEventListener('keydown', this.keydownHandler);
            }
        }

        hide() {
            if (!this.isOpen) return;

            this.element.style.opacity = '0';
            this.element.style.transform = 'translate(-50%, -50%) scale(0.7)';
            this.backdrop.style.opacity = '0';

            setTimeout(() => {
                if (this.element && this.element.parentNode) {
                    this.element.parentNode.removeChild(this.element);
                }
                if (this.backdrop && this.backdrop.parentNode) {
                    this.backdrop.parentNode.removeChild(this.backdrop);
                }
                document.body.style.overflow = '';
                
                if (this.keydownHandler) {
                    document.removeEventListener('keydown', this.keydownHandler);
                }
            }, 300);

            this.isOpen = false;
        }
    }

    // ===== 霓虹灯加载器组件 =====
    class NeonLoader {
        constructor(options = {}) {
            this.options = {
                type: 'spinner', // spinner, pulse, wave
                size: 'medium', // small, medium, large
                color: 'blue', // blue, pink, green, rainbow
                text: '',
                overlay: false,
                ...options
            };
            this.element = null;
        }

        show(target = document.body) {
            this.create();
            target.appendChild(this.element);
            this.animate();
            return this;
        }

        create() {
            const colors = {
                blue: 'var(--neon-electric-blue)',
                pink: 'var(--neon-hot-pink)',
                green: 'var(--neon-lime-green)',
                rainbow: 'conic-gradient(var(--neon-crimson), var(--neon-orange), var(--neon-gold), var(--neon-lime-green), var(--neon-aqua), var(--neon-electric-blue), var(--neon-violet), var(--neon-magenta))'
            };

            const sizes = {
                small: '30px',
                medium: '50px',
                large: '80px'
            };

            this.element = document.createElement('div');
            this.element.className = 'neon-loader';
            
            if (this.options.overlay) {
                this.element.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                `;
            } else {
                this.element.style.cssText = `
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    padding: 2rem;
                `;
            }

            // 创建加载动画
            const loader = document.createElement('div');
            loader.style.cssText = `
                width: ${sizes[this.options.size]};
                height: ${sizes[this.options.size]};
                border-radius: 50%;
                position: relative;
            `;

            if (this.options.type === 'spinner') {
                loader.style.border = `3px solid transparent`;
                loader.style.borderTop = `3px solid ${colors[this.options.color]}`;
                loader.style.animation = 'neon-spin 1s linear infinite';
                loader.style.boxShadow = `0 0 20px ${colors[this.options.color]}40`;
            }

            if (this.options.text) {
                const text = document.createElement('div');
                text.textContent = this.options.text;
                text.style.cssText = `
                    color: white;
                    margin-top: 1rem;
                    font-size: 1rem;
                    text-align: center;
                `;
                this.element.appendChild(text);
            }

            this.element.appendChild(loader);
        }

        animate() {
            this.element.style.opacity = '0';
            requestAnimationFrame(() => {
                this.element.style.transition = 'opacity 0.3s ease';
                this.element.style.opacity = '1';
            });
        }

        hide() {
            this.element.style.opacity = '0';
            setTimeout(() => {
                if (this.element && this.element.parentNode) {
                    this.element.parentNode.removeChild(this.element);
                }
            }, 300);
        }
    }

    // 添加必要的CSS动画
    if (!document.querySelector('#neon-components-styles')) {
        const style = document.createElement('style');
        style.id = 'neon-components-styles';
        style.textContent = `
            @keyframes neon-spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    // 全局API
    window.NeonComponents = {
        Notification: NeonNotification,
        Modal: NeonModal,
        Loader: NeonLoader,
        
        // 便捷方法
        notify: (message, type = 'info', options = {}) => {
            return new NeonNotification({ message, type, ...options }).show();
        },
        
        modal: (content, title = '', options = {}) => {
            return new NeonModal({ content, title, ...options }).show();
        },
        
        loader: (text = '', options = {}) => {
            return new NeonLoader({ text, ...options }).show();
        }
    };

})();
