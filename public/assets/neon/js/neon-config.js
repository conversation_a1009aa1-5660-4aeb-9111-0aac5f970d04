/**
 * Neon Effects Configuration - 霓虹灯效果配置文件
 * Version: 2.0.0
 * Author: Enterprise Development Team
 */

(function() {
    'use strict';

    // ===== 霓虹灯效果配置 =====
    window.NeonConfig = {
        
        // 颜色配置
        colors: {
            primary: '#00d4ff',      // 主要颜色 - 电光蓝
            secondary: '#ff0080',    // 次要颜色 - 热情粉
            success: '#00ff41',      // 成功颜色 - 青柠绿
            warning: '#ffd700',      // 警告颜色 - 金色
            danger: '#dc143c',       // 危险颜色 - 深红色
            info: '#00ffff',         // 信息颜色 - 水蓝色
            
            // 扩展颜色
            violet: '#8a2be2',
            orange: '#ff4500',
            magenta: '#ff00ff',
            springGreen: '#00ff7f'
        },

        // 动画配置
        animations: {
            // 动画持续时间
            duration: {
                fast: '0.5s',
                normal: '1s',
                slow: '2s',
                verySlow: '4s'
            },
            
            // 动画缓动函数
            easing: {
                easeInOut: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                easeOut: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
                elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
            },
            
            // 默认启用的动画
            enabled: {
                flicker: true,
                breathe: true,
                pulse: true,
                rainbow: true,
                wave: true,
                glow: true
            }
        },

        // 强度配置
        intensity: {
            low: '0 0 5px',
            medium: '0 0 10px, 0 0 20px',
            high: '0 0 10px, 0 0 20px, 0 0 40px',
            extreme: '0 0 10px, 0 0 20px, 0 0 40px, 0 0 80px'
        },

        // 组件默认配置
        components: {
            // 通知组件
            notification: {
                duration: 5000,
                position: 'top-right',
                showProgress: true,
                maxVisible: 5,
                stackSpacing: 10
            },
            
            // 模态框组件
            modal: {
                backdrop: true,
                keyboard: true,
                showCloseButton: true,
                size: 'medium',
                animation: 'scale'
            },
            
            // 加载器组件
            loader: {
                type: 'spinner',
                size: 'medium',
                color: 'primary',
                overlay: false,
                text: ''
            }
        },

        // 性能配置
        performance: {
            // 自动检测性能模式
            autoDetect: true,
            
            // 强制性能模式 ('auto', 'high', 'low')
            mode: 'auto',
            
            // 低性能模式下的设置
            lowPerformance: {
                disableAnimations: false,
                reduceEffects: true,
                simplifyGlow: true,
                maxAnimations: 5
            },
            
            // 高性能模式下的设置
            highPerformance: {
                enableAllEffects: true,
                maxAnimations: 20,
                use3D: true,
                useGPU: true
            }
        },

        // 响应式配置
        responsive: {
            // 断点配置
            breakpoints: {
                mobile: 480,
                tablet: 768,
                desktop: 1024,
                large: 1200
            },
            
            // 移动端优化
            mobile: {
                reduceGlow: true,
                simplifyAnimations: true,
                disableParallax: true,
                reducedMotion: true
            }
        },

        // 可访问性配置
        accessibility: {
            // 支持高对比度模式
            highContrast: true,
            
            // 支持减少动画偏好
            respectReducedMotion: true,
            
            // 键盘导航支持
            keyboardNavigation: true,
            
            // 屏幕阅读器支持
            screenReader: true,
            
            // 焦点指示器
            focusIndicator: true
        },

        // 主题配置
        themes: {
            // 默认主题
            default: {
                background: 'linear-gradient(135deg, #0a0e27 0%, #1a1e3a 100%)',
                text: '#ffffff',
                accent: '#00d4ff'
            },
            
            // 深色主题
            dark: {
                background: 'linear-gradient(135deg, #000000 0%, #1a1a1a 100%)',
                text: '#ffffff',
                accent: '#ff0080'
            },
            
            // 浅色主题
            light: {
                background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                text: '#212529',
                accent: '#0066cc'
            }
        },

        // 调试配置
        debug: {
            enabled: false,
            logLevel: 'info', // 'debug', 'info', 'warn', 'error'
            showPerformanceMetrics: false,
            highlightElements: false
        }
    };

    // ===== 配置应用函数 =====
    
    /**
     * 应用配置到CSS变量
     */
    function applyConfigToCSS() {
        const root = document.documentElement;
        const config = window.NeonConfig;
        
        // 应用颜色配置
        Object.entries(config.colors).forEach(([key, value]) => {
            root.style.setProperty(`--neon-${key}`, value);
        });
        
        // 应用强度配置
        Object.entries(config.intensity).forEach(([key, value]) => {
            root.style.setProperty(`--neon-intensity-${key}`, value);
        });
        
        // 应用动画持续时间
        Object.entries(config.animations.duration).forEach(([key, value]) => {
            root.style.setProperty(`--neon-duration-${key}`, value);
        });
    }

    /**
     * 更新配置
     * @param {Object} newConfig - 新的配置对象
     */
    window.NeonConfig.update = function(newConfig) {
        // 深度合并配置
        function deepMerge(target, source) {
            for (const key in source) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    target[key] = target[key] || {};
                    deepMerge(target[key], source[key]);
                } else {
                    target[key] = source[key];
                }
            }
            return target;
        }
        
        deepMerge(this, newConfig);
        applyConfigToCSS();
        
        // 触发配置更新事件
        window.dispatchEvent(new CustomEvent('neonConfigUpdated', {
            detail: { config: this }
        }));
        
        if (this.debug.enabled) {
            console.log('🌟 Neon Config Updated:', this);
        }
    };

    /**
     * 重置配置为默认值
     */
    window.NeonConfig.reset = function() {
        // 这里可以重新加载默认配置
        applyConfigToCSS();
        
        if (this.debug.enabled) {
            console.log('🌟 Neon Config Reset to Default');
        }
    };

    /**
     * 获取当前配置
     * @param {string} path - 配置路径，如 'colors.primary'
     * @returns {*} 配置值
     */
    window.NeonConfig.get = function(path) {
        return path.split('.').reduce((obj, key) => obj && obj[key], this);
    };

    /**
     * 设置配置值
     * @param {string} path - 配置路径
     * @param {*} value - 配置值
     */
    window.NeonConfig.set = function(path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((obj, key) => {
            obj[key] = obj[key] || {};
            return obj[key];
        }, this);
        
        target[lastKey] = value;
        applyConfigToCSS();
        
        if (this.debug.enabled) {
            console.log(`🌟 Neon Config Set: ${path} = ${value}`);
        }
    };

    // ===== 初始化 =====
    
    // 页面加载时应用配置
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', applyConfigToCSS);
    } else {
        applyConfigToCSS();
    }

    // 监听配置更新事件
    window.addEventListener('neonConfigUpdated', function(e) {
        if (window.NeonConfig.debug.enabled) {
            console.log('🌟 Neon Config Event:', e.detail);
        }
    });

    // 导出配置验证函数
    window.NeonConfig.validate = function() {
        const errors = [];
        
        // 验证颜色格式
        Object.entries(this.colors).forEach(([key, value]) => {
            if (!/^#[0-9A-Fa-f]{6}$/.test(value)) {
                errors.push(`Invalid color format for ${key}: ${value}`);
            }
        });
        
        // 验证持续时间格式
        Object.entries(this.animations.duration).forEach(([key, value]) => {
            if (!/^\d+(\.\d+)?(s|ms)$/.test(value)) {
                errors.push(`Invalid duration format for ${key}: ${value}`);
            }
        });
        
        if (errors.length > 0) {
            console.warn('🌟 Neon Config Validation Errors:', errors);
            return false;
        }
        
        if (this.debug.enabled) {
            console.log('🌟 Neon Config Validation: Passed');
        }
        return true;
    };

    // 初始验证
    window.NeonConfig.validate();

    if (window.NeonConfig.debug.enabled) {
        console.log('🌟 Neon Config Initialized:', window.NeonConfig);
    }

})();
