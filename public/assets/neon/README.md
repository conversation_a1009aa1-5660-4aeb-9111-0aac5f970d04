# Neon Effects - 运营级霓虹灯效果库

## 概述

这是一个专业的霓虹灯效果CSS和JavaScript库，专为运营级网站设计，提供丰富的视觉效果和交互体验。

## 文件结构

```
/assets/neon/
├── css/
│   ├── styles.css           # 主题样式和基础霓虹效果
│   ├── neon-effects.css     # 专业霓虹灯效果库
│   └── theme-colors.css     # 主题颜色配置
├── js/
│   ├── scripts.js           # 主要脚本和效果管理器
│   ├── neon-components.js   # 霓虹灯UI组件库
│   └── enterprise.js        # 企业级增强功能
├── demo.html               # 效果演示页面
└── README.md              # 使用说明文档
```

## 快速开始

### 1. 引入CSS文件

```html
<!-- 基础样式 -->
<link href="/assets/neon/css/styles.css" rel="stylesheet">
<!-- 霓虹灯效果 -->
<link href="/assets/neon/css/neon-effects.css" rel="stylesheet">
<!-- 主题颜色 -->
<link href="/assets/neon/css/theme-colors.css" rel="stylesheet">
```

### 2. 引入JavaScript文件

```html
<!-- 主要脚本 -->
<script src="/assets/neon/js/scripts.js"></script>
<!-- 组件库 -->
<script src="/assets/neon/js/neon-components.js"></script>
```

### 3. 基础使用

```html
<!-- 霓虹灯文字 -->
<h1 class="neon-text-base neon-text-lg neon-electric-blue">
    霓虹灯标题
</h1>

<!-- 霓虹灯按钮 -->
<button class="neon-button neon-blue">
    点击我
</button>

<!-- 霓虹灯卡片 -->
<div class="neon-card">
    <h3>卡片标题</h3>
    <p>卡片内容</p>
</div>
```

## CSS类说明

### 文字效果类

#### 基础类
- `neon-text-base` - 霓虹灯文字基础样式

#### 尺寸类
- `neon-text-sm` - 小号文字 (1rem)
- `neon-text-md` - 中号文字 (1.5rem)
- `neon-text-lg` - 大号文字 (2.5rem)
- `neon-text-xl` - 超大文字 (4rem)
- `neon-text-xxl` - 巨大文字 (6rem)

#### 颜色类
- `neon-electric-blue` - 电光蓝
- `neon-hot-pink` - 热情粉
- `neon-lime-green` - 青柠绿
- `neon-violet` - 紫罗兰
- `neon-orange` - 橙色
- `neon-crimson` - 深红色
- `neon-gold` - 金色
- `neon-aqua` - 水蓝色

#### 动画类
- `neon-flicker-advanced` - 高级闪烁效果
- `neon-breathe` - 呼吸效果
- `neon-pulse-strong` - 强脉冲效果
- `neon-rainbow-cycle` - 彩虹循环效果
- `neon-wave` - 波浪效果

### 按钮效果类

```html
<button class="neon-button neon-blue">蓝色按钮</button>
<button class="neon-button neon-pink">粉色按钮</button>
<button class="neon-button neon-green">绿色按钮</button>
```

### 卡片效果类

```html
<!-- 霓虹边框卡片 -->
<div class="neon-card">内容</div>

<!-- 发光背景卡片 -->
<div class="neon-bg-glow">内容</div>

<!-- 霓虹边框效果 -->
<div class="neon-border neon-electric-blue">内容</div>
```

### 输入框效果类

```html
<input type="text" class="neon-input neon-blue" placeholder="输入内容">
<input type="email" class="neon-input neon-pink" placeholder="邮箱">
<input type="password" class="neon-input neon-green" placeholder="密码">
```

## JavaScript API

### 霓虹灯效果管理器

```javascript
// 获取管理器实例
const manager = window.NeonEffects.manager();

// 添加效果
const effectId = window.NeonEffects.add(element, 'pulse', options);

// 移除效果
window.NeonEffects.remove(effectId);

// 切换效果
window.NeonEffects.toggle('.my-elements', 'neon-flicker');
```

### 组件库

#### 通知组件

```javascript
// 显示通知
NeonComponents.notify('消息内容', 'success', {
    title: '成功',
    duration: 3000,
    position: 'top-right'
});

// 或使用完整API
const notification = new NeonComponents.Notification({
    type: 'info',
    title: '提示',
    message: '这是一条消息',
    duration: 5000,
    position: 'top-right',
    showProgress: true
});
notification.show();
```

#### 模态框组件

```javascript
// 显示模态框
NeonComponents.modal('内容', '标题', {
    size: 'medium',
    backdrop: true,
    keyboard: true
});

// 或使用完整API
const modal = new NeonComponents.Modal({
    title: '模态框标题',
    content: '<p>模态框内容</p>',
    size: 'large',
    showCloseButton: true
});
modal.show();
```

#### 加载器组件

```javascript
// 显示加载器
const loader = NeonComponents.loader('加载中...', {
    type: 'spinner',
    size: 'medium',
    color: 'blue',
    overlay: true
});

// 隐藏加载器
setTimeout(() => {
    loader.hide();
}, 3000);
```

## 主题配置

### CSS变量

```css
:root {
    /* 霓虹灯颜色 */
    --neon-electric-blue: #00d4ff;
    --neon-hot-pink: #ff0080;
    --neon-lime-green: #00ff41;
    --neon-violet: #8a2be2;
    --neon-orange: #ff4500;
    --neon-crimson: #dc143c;
    --neon-gold: #ffd700;
    --neon-aqua: #00ffff;
    
    /* 霓虹灯强度 */
    --neon-intensity-low: 0 0 5px;
    --neon-intensity-medium: 0 0 10px, 0 0 20px;
    --neon-intensity-high: 0 0 10px, 0 0 20px, 0 0 40px;
    --neon-intensity-extreme: 0 0 10px, 0 0 20px, 0 0 40px, 0 0 80px;
}
```

### 主题切换

```javascript
// 应用主题
document.documentElement.setAttribute('data-bs-theme', 'dark');

// 监听主题变更
window.addEventListener('neonThemeChanged', (e) => {
    console.log('主题已切换到:', e.detail.theme);
});
```

## 性能优化

### 自动性能检测

系统会自动检测用户设备性能和网络状况：

- 低端设备或慢速网络：自动降低动画复杂度
- 用户偏好设置：支持 `prefers-reduced-motion`
- 页面可见性：页面隐藏时暂停动画

### 手动性能控制

```javascript
// 检测性能模式
const manager = window.NeonEffects.manager();
console.log('性能模式:', manager.performanceMode); // 'high' 或 'low'

// 手动设置性能模式
document.body.classList.add('neon-performance-mode');
```

## 响应式设计

所有效果都支持响应式设计：

```css
/* 平板设备 */
@media (max-width: 768px) {
    .neon-text-xl { font-size: 2.5rem; }
    .neon-text-xxl { font-size: 3.5rem; }
}

/* 手机设备 */
@media (max-width: 480px) {
    .neon-text-lg { font-size: 1.8rem; }
    .neon-text-xl { font-size: 2rem; }
}
```

## 可访问性

### 高对比度支持

```css
@media (prefers-contrast: high) {
    :root {
        --neon-intensity-low: 0 0 2px;
        --neon-intensity-medium: 0 0 4px;
        --neon-intensity-high: 0 0 6px;
        --neon-intensity-extreme: 0 0 8px;
    }
}
```

### 键盘导航

所有交互元素都支持键盘导航和焦点指示器。

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 许可证

MIT License

## 更新日志

### v2.0.0
- 新增运营级霓虹灯效果库
- 添加组件化JavaScript API
- 支持性能自动优化
- 增强响应式设计
- 改进可访问性支持

## 技术支持

如有问题或建议，请联系开发团队。
