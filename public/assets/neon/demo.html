<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neon Effects Demo - 霓虹灯效果演示</title>
    
    <!-- 引入CSS文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <link href="css/neon-effects.css" rel="stylesheet">
    <link href="css/theme-colors.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #0a0e27 0%, #1a1e3a 100%);
            min-height: 100vh;
            color: white;
            font-family: 'Arial', sans-serif;
        }
        
        .demo-section {
            margin: 3rem 0;
            padding: 2rem;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .demo-item {
            padding: 1.5rem;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .color-palette {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            justify-content: center;
            margin: 2rem 0;
        }
        
        .color-sample {
            width: 100px;
            height: 100px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            text-shadow: 0 0 10px currentColor;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .color-sample:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <!-- 标题区域 -->
        <div class="text-center mb-5">
            <h1 class="neon-text-base neon-text-xxl neon-electric-blue neon-pulse-strong">
                NEON EFFECTS
            </h1>
            <p class="lead neon-text-base neon-hot-pink">运营级霓虹灯效果演示</p>
        </div>

        <!-- 文字效果演示 -->
        <div class="demo-section">
            <h2 class="neon-text-base neon-text-lg neon-lime-green mb-4">文字效果演示</h2>
            
            <div class="demo-grid">
                <div class="demo-item">
                    <h3 class="neon-text-base neon-text-md neon-electric-blue">电光蓝</h3>
                    <p class="neon-text-base neon-electric-blue">Electric Blue</p>
                </div>
                
                <div class="demo-item">
                    <h3 class="neon-text-base neon-text-md neon-hot-pink">热情粉</h3>
                    <p class="neon-text-base neon-hot-pink">Hot Pink</p>
                </div>
                
                <div class="demo-item">
                    <h3 class="neon-text-base neon-text-md neon-lime-green">青柠绿</h3>
                    <p class="neon-text-base neon-lime-green">Lime Green</p>
                </div>
            </div>
        </div>

        <!-- 动画效果演示 -->
        <div class="demo-section">
            <h2 class="neon-text-base neon-text-lg neon-lime-green mb-4">动画效果演示</h2>
            
            <div class="demo-grid">
                <div class="demo-item">
                    <h3 class="neon-text-base neon-text-md neon-electric-blue neon-flicker-advanced">闪烁效果</h3>
                    <p>Flicker Animation</p>
                </div>
                
                <div class="demo-item">
                    <h3 class="neon-text-base neon-text-md neon-hot-pink neon-breathe">呼吸效果</h3>
                    <p>Breathe Animation</p>
                </div>
                
                <div class="demo-item">
                    <h3 class="neon-text-base neon-text-md neon-pulse-strong">脉冲效果</h3>
                    <p>Pulse Animation</p>
                </div>
                
                <div class="demo-item">
                    <h3 class="neon-text-base neon-text-md neon-rainbow-cycle">彩虹循环</h3>
                    <p>Rainbow Cycle</p>
                </div>
                
                <div class="demo-item">
                    <h3 class="neon-text-base neon-text-md neon-electric-blue neon-wave">波浪效果</h3>
                    <p>Wave Animation</p>
                </div>
            </div>
        </div>

        <!-- 按钮效果演示 -->
        <div class="demo-section">
            <h2 class="neon-text-base neon-text-lg neon-lime-green mb-4">按钮效果演示</h2>
            
            <div class="demo-grid">
                <div class="demo-item">
                    <button class="neon-button neon-blue">蓝色按钮</button>
                </div>
                
                <div class="demo-item">
                    <button class="neon-button neon-pink">粉色按钮</button>
                </div>
                
                <div class="demo-item">
                    <button class="neon-button neon-green">绿色按钮</button>
                </div>
            </div>
        </div>

        <!-- 卡片效果演示 -->
        <div class="demo-section">
            <h2 class="neon-text-base neon-text-lg neon-lime-green mb-4">卡片效果演示</h2>
            
            <div class="demo-grid">
                <div class="neon-card">
                    <h4 class="neon-text-base neon-electric-blue">霓虹卡片</h4>
                    <p>这是一个带有霓虹边框效果的卡片，鼠标悬停查看3D效果。</p>
                </div>
                
                <div class="neon-bg-glow p-4">
                    <h4 class="neon-text-base neon-hot-pink">发光背景</h4>
                    <p>这是一个带有发光背景效果的卡片。</p>
                </div>
            </div>
        </div>

        <!-- 输入框效果演示 -->
        <div class="demo-section">
            <h2 class="neon-text-base neon-text-lg neon-lime-green mb-4">输入框效果演示</h2>
            
            <div class="demo-grid">
                <div class="demo-item">
                    <input type="text" class="neon-input neon-blue" placeholder="蓝色输入框">
                </div>
                
                <div class="demo-item">
                    <input type="email" class="neon-input neon-pink" placeholder="粉色输入框">
                </div>
                
                <div class="demo-item">
                    <input type="password" class="neon-input neon-green" placeholder="绿色输入框">
                </div>
            </div>
        </div>

        <!-- 加载器演示 -->
        <div class="demo-section">
            <h2 class="neon-text-base neon-text-lg neon-lime-green mb-4">加载器演示</h2>
            
            <div class="demo-grid">
                <div class="demo-item">
                    <div class="neon-loader"></div>
                    <p class="mt-3">霓虹加载器</p>
                </div>
            </div>
        </div>

        <!-- 颜色调色板 -->
        <div class="demo-section">
            <h2 class="neon-text-base neon-text-lg neon-lime-green mb-4">颜色调色板</h2>
            
            <div class="color-palette">
                <div class="color-sample" style="background: var(--neon-electric-blue); color: var(--neon-electric-blue);">
                    Electric Blue
                </div>
                <div class="color-sample" style="background: var(--neon-hot-pink); color: var(--neon-hot-pink);">
                    Hot Pink
                </div>
                <div class="color-sample" style="background: var(--neon-lime-green); color: var(--neon-lime-green);">
                    Lime Green
                </div>
                <div class="color-sample" style="background: var(--neon-violet); color: var(--neon-violet);">
                    Violet
                </div>
                <div class="color-sample" style="background: var(--neon-orange); color: var(--neon-orange);">
                    Orange
                </div>
                <div class="color-sample" style="background: var(--neon-crimson); color: var(--neon-crimson);">
                    Crimson
                </div>
                <div class="color-sample" style="background: var(--neon-gold); color: var(--neon-gold);">
                    Gold
                </div>
                <div class="color-sample" style="background: var(--neon-aqua); color: var(--neon-aqua);">
                    Aqua
                </div>
            </div>
        </div>

        <!-- 交互演示 -->
        <div class="demo-section">
            <h2 class="neon-text-base neon-text-lg neon-lime-green mb-4">交互演示</h2>
            
            <div class="text-center">
                <button class="btn btn-primary me-3" onclick="showNotification()">
                    <i class="fas fa-bell me-2"></i>显示通知
                </button>
                
                <button class="btn btn-success me-3" onclick="showModal()">
                    <i class="fas fa-window-maximize me-2"></i>显示模态框
                </button>
                
                <button class="btn btn-warning" onclick="showLoader()">
                    <i class="fas fa-spinner me-2"></i>显示加载器
                </button>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/scripts.js"></script>
    <script src="js/neon-components.js"></script>
    
    <script>
        // 演示函数
        function showNotification() {
            NeonComponents.notify('这是一个霓虹灯通知！', 'success', {
                title: '成功',
                duration: 3000
            });
        }
        
        function showModal() {
            NeonComponents.modal(
                '<p>这是一个霓虹灯模态框的内容。</p><p>具有炫酷的霓虹灯边框效果。</p>',
                '霓虹灯模态框',
                { size: 'medium' }
            );
        }
        
        function showLoader() {
            const loader = NeonComponents.loader('加载中...', { 
                overlay: true,
                type: 'spinner',
                color: 'blue'
            });
            
            setTimeout(() => {
                loader.hide();
                showNotification();
            }, 3000);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌟 Neon Effects Demo 已加载');
            
            // 添加一些交互效果
            document.querySelectorAll('.color-sample').forEach(sample => {
                sample.addEventListener('click', function() {
                    const color = this.style.color;
                    NeonComponents.notify(`选择了颜色: ${this.textContent}`, 'info', {
                        duration: 2000
                    });
                });
            });
        });
    </script>
</body>
</html>
