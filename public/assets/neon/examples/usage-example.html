<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neon Effects Usage Example - 使用示例</title>
    
    <!-- 引入CSS文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../css/styles.css" rel="stylesheet">
    <link href="../css/neon-effects.css" rel="stylesheet">
    <link href="../css/theme-colors.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #0a0e27 0%, #1a1e3a 100%);
            min-height: 100vh;
            color: white;
            font-family: 'Arial', sans-serif;
        }
        
        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero-content {
            z-index: 2;
            position: relative;
        }
        
        .hero-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
            animation: neon-bg-pulse 4s ease-in-out infinite;
        }
        
        .feature-card {
            background: rgba(0, 0, 0, 0.6);
            border-radius: 15px;
            padding: 2rem;
            margin: 1rem 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .pricing-card {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 20px;
            padding: 2.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .pricing-card.featured {
            border: 2px solid var(--neon-electric-blue);
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
        }
        
        .pricing-card.featured::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(
                from 0deg,
                transparent,
                var(--neon-electric-blue),
                transparent,
                var(--neon-hot-pink),
                transparent
            );
            animation: neon-border-spin 4s linear infinite;
            z-index: -1;
        }
        
        .pricing-card.featured::after {
            content: '';
            position: absolute;
            inset: 2px;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 18px;
            z-index: -1;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top" style="background: rgba(0, 0, 0, 0.9); backdrop-filter: blur(10px);">
        <div class="container">
            <a class="navbar-brand neon-text-base neon-electric-blue" href="#">
                <i class="fas fa-bolt me-2"></i>NeonSite
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link neon-text-base" href="#home">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link neon-text-base" href="#features">特性</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link neon-text-base" href="#pricing">价格</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link neon-text-base" href="#contact">联系</a>
                    </li>
                </ul>
                
                <button class="btn neon-button neon-blue ms-3" onclick="showLoginModal()">
                    <i class="fas fa-sign-in-alt me-2"></i>登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section id="home" class="hero-section">
        <div class="hero-bg"></div>
        <div class="hero-content">
            <h1 class="neon-text-base neon-text-xxl neon-electric-blue neon-pulse-strong mb-4">
                NEON WEBSITE
            </h1>
            <p class="neon-text-base neon-text-lg neon-hot-pink neon-breathe mb-5">
                体验未来科技的霓虹之美
            </p>
            <div class="d-flex flex-wrap justify-content-center gap-3">
                <button class="neon-button neon-blue" onclick="showNotification('欢迎来到霓虹世界！', 'success')">
                    <i class="fas fa-rocket me-2"></i>开始体验
                </button>
                <button class="neon-button neon-pink" onclick="scrollToSection('features')">
                    <i class="fas fa-info-circle me-2"></i>了解更多
                </button>
            </div>
        </div>
    </section>

    <!-- 特性区域 -->
    <section id="features" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="neon-text-base neon-text-xl neon-lime-green neon-flicker-advanced">
                    核心特性
                </h2>
                <p class="lead neon-text-base">探索霓虹灯效果的无限可能</p>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-card neon-border neon-electric-blue">
                        <div class="text-center mb-3">
                            <i class="fas fa-palette fa-3x neon-text-base neon-electric-blue"></i>
                        </div>
                        <h4 class="neon-text-base neon-electric-blue">丰富色彩</h4>
                        <p>8种预设霓虹灯颜色，支持自定义配色方案，满足各种设计需求。</p>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="feature-card neon-border neon-hot-pink">
                        <div class="text-center mb-3">
                            <i class="fas fa-magic fa-3x neon-text-base neon-hot-pink"></i>
                        </div>
                        <h4 class="neon-text-base neon-hot-pink">动画效果</h4>
                        <p>多种动画效果：闪烁、呼吸、脉冲、彩虹循环等，让页面生动起来。</p>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="feature-card neon-border neon-lime-green">
                        <div class="text-center mb-3">
                            <i class="fas fa-mobile-alt fa-3x neon-text-base neon-lime-green"></i>
                        </div>
                        <h4 class="neon-text-base neon-lime-green">响应式设计</h4>
                        <p>完美适配各种设备，自动优化性能，确保最佳用户体验。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 价格区域 -->
    <section id="pricing" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="neon-text-base neon-text-xl neon-gold neon-rainbow-cycle">
                    价格方案
                </h2>
                <p class="lead neon-text-base">选择适合您的霓虹灯效果方案</p>
            </div>
            
            <div class="row justify-content-center">
                <div class="col-md-4 mb-4">
                    <div class="pricing-card">
                        <h3 class="neon-text-base neon-electric-blue">基础版</h3>
                        <div class="neon-text-base neon-text-lg neon-electric-blue my-3">
                            ¥99<small>/月</small>
                        </div>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>基础霓虹效果</li>
                            <li><i class="fas fa-check text-success me-2"></i>5种颜色选择</li>
                            <li><i class="fas fa-check text-success me-2"></i>基础动画</li>
                        </ul>
                        <button class="neon-button neon-blue w-100">选择方案</button>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="pricing-card featured">
                        <div class="badge bg-primary mb-3">推荐</div>
                        <h3 class="neon-text-base neon-hot-pink">专业版</h3>
                        <div class="neon-text-base neon-text-lg neon-hot-pink my-3">
                            ¥199<small>/月</small>
                        </div>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>全部霓虹效果</li>
                            <li><i class="fas fa-check text-success me-2"></i>8种颜色选择</li>
                            <li><i class="fas fa-check text-success me-2"></i>高级动画</li>
                            <li><i class="fas fa-check text-success me-2"></i>自定义配置</li>
                        </ul>
                        <button class="neon-button neon-pink w-100">选择方案</button>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="pricing-card">
                        <h3 class="neon-text-base neon-lime-green">企业版</h3>
                        <div class="neon-text-base neon-text-lg neon-lime-green my-3">
                            ¥399<small>/月</small>
                        </div>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>所有功能</li>
                            <li><i class="fas fa-check text-success me-2"></i>无限颜色</li>
                            <li><i class="fas fa-check text-success me-2"></i>定制开发</li>
                            <li><i class="fas fa-check text-success me-2"></i>技术支持</li>
                        </ul>
                        <button class="neon-button neon-green w-100">联系我们</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系区域 -->
    <section id="contact" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="neon-text-base neon-text-xl neon-violet neon-wave">
                    联系我们
                </h2>
                <p class="lead neon-text-base">有任何问题？我们随时为您服务</p>
            </div>
            
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <form class="neon-bg-glow p-4">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label neon-text-base">姓名</label>
                                <input type="text" class="neon-input neon-blue" placeholder="请输入您的姓名">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label neon-text-base">邮箱</label>
                                <input type="email" class="neon-input neon-pink" placeholder="请输入您的邮箱">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label neon-text-base">主题</label>
                            <input type="text" class="neon-input neon-green" placeholder="请输入主题">
                        </div>
                        <div class="mb-3">
                            <label class="form-label neon-text-base">消息</label>
                            <textarea class="neon-input neon-violet" rows="5" placeholder="请输入您的消息"></textarea>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="neon-button neon-blue" onclick="handleSubmit(event)">
                                <i class="fas fa-paper-plane me-2"></i>发送消息
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="py-4 text-center" style="background: rgba(0, 0, 0, 0.8);">
        <div class="container">
            <p class="neon-text-base neon-electric-blue mb-0">
                © 2024 NeonSite. All rights reserved. Powered by Neon Effects.
            </p>
        </div>
    </footer>

    <!-- 引入JavaScript文件 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../js/neon-config.js"></script>
    <script src="../js/scripts.js"></script>
    <script src="../js/neon-components.js"></script>
    
    <script>
        // 页面功能函数
        function showNotification(message, type = 'info') {
            NeonComponents.notify(message, type, {
                duration: 3000,
                position: 'top-right'
            });
        }
        
        function showLoginModal() {
            const loginForm = `
                <form>
                    <div class="mb-3">
                        <label class="form-label neon-text-base">用户名</label>
                        <input type="text" class="neon-input neon-blue w-100" placeholder="请输入用户名">
                    </div>
                    <div class="mb-3">
                        <label class="form-label neon-text-base">密码</label>
                        <input type="password" class="neon-input neon-pink w-100" placeholder="请输入密码">
                    </div>
                    <div class="text-center">
                        <button type="submit" class="neon-button neon-blue me-2">登录</button>
                        <button type="button" class="neon-button neon-green">注册</button>
                    </div>
                </form>
            `;
            
            NeonComponents.modal(loginForm, '用户登录', {
                size: 'medium'
            });
        }
        
        function scrollToSection(sectionId) {
            document.getElementById(sectionId).scrollIntoView({
                behavior: 'smooth'
            });
        }
        
        function handleSubmit(event) {
            event.preventDefault();
            
            const loader = NeonComponents.loader('发送中...', {
                overlay: true,
                color: 'blue'
            });
            
            setTimeout(() => {
                loader.hide();
                showNotification('消息发送成功！', 'success');
            }, 2000);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌟 Neon Website Example 已加载');
            
            // 显示欢迎通知
            setTimeout(() => {
                showNotification('欢迎访问霓虹灯效果演示网站！', 'info');
            }, 1000);
            
            // 自定义配置示例
            NeonConfig.update({
                animations: {
                    duration: {
                        fast: '0.3s',
                        normal: '0.8s'
                    }
                },
                debug: {
                    enabled: true,
                    logLevel: 'info'
                }
            });
        });
    </script>
</body>
</html>
